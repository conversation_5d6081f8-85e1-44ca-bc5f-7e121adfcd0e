import os
import logging
import json
from datetime import datetime
from werkzeug.utils import secure_filename
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

from get_vector_db import get_vector_db
from create_temp_dirs import create_pdf_directory_structure
from db_pdf_processor import process_pdf_db_first
import db_content_utils as db
from embed import scrape_url

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./_temp")

def embed_file_db_first(file, category, source_url=None, use_vision=None, filter_sensitivity=None, max_images=None, force_update=False):
    """
    Embed a PDF file into the vector database using the database-first retrieval approach.

    This function prioritizes database queries over network requests for better performance.

    Args:
        file: The uploaded file object
        category: The category for organizing content
        source_url: Original URL where the PDF was obtained
        use_vision: Whether to use vision model for image analysis
        filter_sensitivity: Sensitivity level for filtering images
        max_images: Maximum number of images to save
        force_update: Force update of URL content even if it's fresh

    Returns:
        tuple: (success, message)
    """
    try:
        if not file or not category:
            return False, "File and category are required"

        # Create a timestamped filename to avoid collisions
        filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{secure_filename(file.filename)}"
        original_filename = file.filename

        # Create the directory structure for this PDF
        dir_structure = create_pdf_directory_structure(category, filename)
        if not dir_structure:
            logger.error(f"Failed to create directory structure for {filename}")
            return False, f"Failed to create directory structure for {filename}"

        # Get the path to save the PDF
        dest = dir_structure["pdf_path"]

        # Save the uploaded file
        file.save(dest)

        # Process the PDF with database-first approach
        logger.info(f"Processing PDF {file.filename} with database-first approach...")
        if use_vision:
            logger.info(f"Vision model analysis enabled with sensitivity: {filter_sensitivity}, max images: {max_images}")
        else:
            logger.info("Vision model analysis disabled")

        # Process the PDF using the database-first approach
        pdf_info = process_pdf_db_first(
            dest,
            category,
            source_url,
            use_vision=use_vision,
            filter_sensitivity=filter_sensitivity,
            max_images=max_images,
            force_update=force_update
        )

        # Convert the processed PDF to document chunks for vector storage
        chunks = []

        if pdf_info["text"]:
            # Create documents from the processed PDF
            documents = []
            for page in pdf_info["text"]:
                page_num = page["page"]
                page_text = page["text"]

                # Create metadata
                metadata = {
                    "source": filename,
                    "original_filename": original_filename,
                    "citation_filename": original_filename,  # Add this field specifically for citations
                    "page": page_num,
                    "type": "pdf",
                    "extraction_method": page.get("extraction_method", "standard")
                }

                # Add source URL if provided
                if source_url:
                    metadata["original_url"] = source_url

                # Add category
                metadata["category"] = category

                # Add database retrieval info
                if pdf_info["metadata"].get("database_retrieval"):
                    metadata["database_retrieval"] = True
                    metadata["url_last_scraped"] = pdf_info["metadata"].get("url_last_scraped")

                # Add source URL ID if available
                if pdf_info["metadata"].get("source_url_id"):
                    metadata["source_url_id"] = pdf_info["metadata"].get("source_url_id")

                # Add PDF document ID if available
                if pdf_info["metadata"].get("pdf_document_id"):
                    metadata["pdf_document_id"] = pdf_info["metadata"].get("pdf_document_id")

                # Add cover image ID if available
                if pdf_info["metadata"].get("cover_image_id"):
                    metadata["cover_image_id"] = pdf_info["metadata"].get("cover_image_id")

                # Add images for this page
                page_images = [img for img in pdf_info["images"] if img.get("page") == page_num]
                if page_images:
                    metadata["images"] = json.dumps(page_images)
                    metadata["image_count"] = len(page_images)

                # Add links
                if pdf_info["links"]:
                    metadata["pdf_links"] = json.dumps(pdf_info["links"])
                    metadata["link_count"] = len(pdf_info["links"])

                # Create document
                documents.append(Document(page_content=page_text, metadata=metadata))

            # Split into chunks for better retrieval
            splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=250)
            chunks = splitter.split_documents(documents)

            # Ensure all chunks have the source filename and URL if provided
            for doc in chunks:
                doc.metadata["source"] = filename
                doc.metadata["original_filename"] = original_filename
                doc.metadata["citation_filename"] = original_filename
                doc.metadata["type"] = "pdf"
                if source_url:
                    doc.metadata["original_url"] = source_url

        if not chunks:
            return False, f"Failed to extract text from {file.filename}"

        # Add to vector database
        db = get_vector_db(category)
        db.add_documents(chunks)

        # Get metadata from the processed PDF
        image_count = pdf_info["metadata"]["image_count"]
        table_count = pdf_info["metadata"]["table_count"]
        link_count = pdf_info["metadata"]["link_count"]

        # Add vision analysis metadata to success message
        images_filtered = pdf_info["metadata"].get("images_filtered", 0)
        vision_enabled = pdf_info["metadata"].get("vision_enabled", False)

        # Add the PDF directory path to the metadata for all chunks
        pdf_base_name = os.path.splitext(filename)[0]
        pdf_dir_path = f"/{category}/{pdf_base_name}"

        # Build success message
        success_message = f"Successfully embedded {file.filename} with {len(chunks)} chunks"
        if image_count > 0:
            success_message += f", {image_count} images"
            # Add vision analysis details if enabled
            if vision_enabled:
                success_message += f" ({images_filtered} filtered by vision model)"
        if table_count > 0:
            success_message += f", {table_count} tables"
        if link_count > 0:
            success_message += f", {link_count} links"

        # Add database retrieval info to success message
        if pdf_info["metadata"].get("database_retrieval"):
            success_message += " (used database content for URL)"

        logger.info(success_message)
        if source_url:
            logger.info(f"PDF {file.filename} linked to source URL: {source_url}")

        return True, success_message
    except Exception as e:
        logger.error(f"Failed to embed file {file.filename}: {str(e)}")
        return False, f"Failed to embed {file.filename}: {str(e)}"

def scrape_url_db_first(url, category, depth=0, force_update=False):
    """
    Scrape a URL with database-first retrieval approach.

    This function checks if URL content exists in the database before scraping.
    It prioritizes database queries over network requests for better performance.

    Args:
        url (str): The URL to scrape
        category (str): The category for organizing content
        depth (int): The depth of links to follow (0-3)
        force_update (bool): Force update of URL content even if it's fresh

    Returns:
        tuple: (success, message, data)
    """
    try:
        if not url or not category:
            return False, "URL and category are required", None

        # Limit depth to a reasonable range
        if depth < 0:
            depth = 0
        if depth > 3:
            depth = 3

        # Check if URL exists in database
        url_record = db.get_source_url_by_url(url)
        url_content_retrieved = False
        scraped_data = None

        # Determine if we should use database content or scrape
        if url_record and db.is_url_content_fresh(url_record) and not force_update:
            logger.info(f"Using fresh database content for URL: {url}")
            source_url_id = url_record['id']

            # Get URL content from database
            url_images = db.get_url_images(source_url_id)
            url_links = db.get_url_links(source_url_id)
            url_text = db.get_url_text(source_url_id)

            # Process images to ensure they're valid strings
            processed_images = []
            for img in url_images:
                img_url = img['url']
                if isinstance(img_url, str) and img_url.startswith(('http://', 'https://')):
                    processed_images.append(img_url)
                else:
                    logger.warning(f"Skipping invalid image URL: {img_url}")

            # Process links to ensure they're valid strings
            processed_links = []
            for link in url_links:
                link_url = link['url']
                if isinstance(link_url, str) and link_url.startswith(('http://', 'https://')):
                    processed_links.append(link_url)
                else:
                    logger.warning(f"Skipping invalid link URL: {link_url}")

            # Create scraped data structure
            scraped_data = {
                "url": url,
                "text": url_text,
                "images": processed_images,
                "links": processed_links,
                "pages": [{
                    "url": url,
                    "text": url_text,
                    "images": processed_images,
                    "links": processed_links,
                    "depth": 0
                }],
                "pages_scraped": 1,
                "max_depth_reached": 0,
                "database_retrieval": True,
                "url_last_scraped": url_record['last_scraped']
            }

            url_content_retrieved = True
        else:
            # Need to scrape the URL
            logger.info(f"Scraping URL content for: {url} with depth {depth}")

            # Use the original scrape_url function
            scraped_data = scrape_url(url, depth)

            if scraped_data and not scraped_data.get("error"):
                # Get title and description if available
                title = None
                description = None

                # Try to extract title and description from the first page
                if scraped_data.get("pages") and len(scraped_data["pages"]) > 0:
                    first_page = scraped_data["pages"][0]

                    # Extract title from the first line if possible
                    text_lines = first_page.get("text", "").split('\n')
                    if text_lines and len(text_lines) > 0:
                        title = text_lines[0][:200]  # Limit title length

                    # Use the first paragraph as description
                    if len(text_lines) > 1:
                        description = text_lines[1][:500]  # Limit description length

                # Insert or update source URL record
                source_url_id = db.insert_source_url(
                    url,
                    title=title,
                    description=description,
                    status='active'
                )

                if source_url_id:
                    # Process each page
                    for i, page in enumerate(scraped_data.get("pages", [])):
                        page_url = page.get("url", url)
                        page_text = page.get("text", "")
                        page_images = page.get("images", [])
                        page_links = page.get("links", [])

                        # Store text content
                        if page_text:
                            # Split text into manageable chunks if it's very large
                            max_chunk_size = 10000  # Characters per chunk
                            if len(page_text) > max_chunk_size:
                                chunks = [page_text[i:i+max_chunk_size]
                                         for i in range(0, len(page_text), max_chunk_size)]
                                for j, chunk in enumerate(chunks):
                                    db.insert_url_content(source_url_id, 'text', chunk, i * 100 + j)
                            else:
                                db.insert_url_content(source_url_id, 'text', page_text, i * 100)

                        # Store images
                        for j, img_url in enumerate(page_images):
                            metadata = {
                                "page_url": page_url,
                                "page_index": i,
                                "index": j,
                                "source": "url_scrape"
                            }
                            db.insert_url_content(source_url_id, 'image', img_url, i * 100 + j, metadata)

                        # Store links
                        for j, link_url in enumerate(page_links):
                            metadata = {
                                "page_url": page_url,
                                "page_index": i,
                                "index": j,
                                "source": "url_scrape"
                            }
                            db.insert_url_content(source_url_id, 'link', link_url, i * 100 + j, metadata)

                    # Add database info to scraped data
                    scraped_data["database_retrieval"] = False
                    scraped_data["source_url_id"] = source_url_id

                    url_content_retrieved = True
                else:
                    logger.error(f"Failed to insert source URL record for {url}")
            else:
                error_msg = scraped_data.get("error", "Unknown error") if scraped_data else "Failed to scrape URL"
                logger.error(f"Failed to scrape URL {url}: {error_msg}")

                # Record the error in the database
                source_url_id = db.insert_source_url(
                    url,
                    status='error',
                    error_message=error_msg
                )

                return False, f"Failed to scrape URL {url}: {error_msg}", None

        if not url_content_retrieved or not scraped_data:
            return False, f"Failed to retrieve content for URL {url}", None

        return True, "URL content retrieved successfully", scraped_data
    except Exception as e:
        logger.error(f"Error processing URL {url}: {str(e)}")
        return False, f"Error processing URL {url}: {str(e)}", None
