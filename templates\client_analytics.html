<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Analytics - {{ summary.client_name }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind for dark mode
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {}
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment"></script>

    <!-- Include utilities.js for theme toggle functionality -->
    <script src="/static/js/utilities.js"></script>

    <!-- Include dark-mode.css for consistent dark mode styling -->
    <link rel="stylesheet" href="/static/css/dark-mode.css">

    <style>
        /* Dark mode styles for analytics */
        .dark .bg-white { background-color: #1f2937 !important; }
        .dark .bg-gray-50 { background-color: #374151 !important; }
        .dark .bg-gray-100 { background-color: #1f2937 !important; }

        /* Text colors in dark mode */
        .dark .text-gray-800 { color: #f3f4f6 !important; }
        .dark .text-gray-700 { color: #e5e7eb !important; }
        .dark .text-gray-600 { color: #d1d5db !important; }
        .dark .text-gray-500 { color: #9ca3af !important; }

        /* Metric cards in dark mode */
        .dark .bg-blue-50 { background-color: #1e3a8a !important; }
        .dark .bg-green-50 { background-color: #065f46 !important; }
        .dark .bg-purple-50 { background-color: #5b21b6 !important; }
        .dark .bg-yellow-50 { background-color: #92400e !important; }

        .dark .border-blue-100 { border-color: #1e40af !important; }
        .dark .border-green-100 { border-color: #047857 !important; }
        .dark .border-purple-100 { border-color: #7c3aed !important; }
        .dark .border-yellow-100 { border-color: #b45309 !important; }

        .dark .text-blue-800 { color: #bfdbfe !important; }
        .dark .text-green-800 { color: #a7f3d0 !important; }
        .dark .text-purple-800 { color: #ddd6fe !important; }
        .dark .text-yellow-800 { color: #fef3c7 !important; }

        .dark .text-blue-600 { color: #60a5fa !important; }
        .dark .text-green-600 { color: #34d399 !important; }
        .dark .text-purple-600 { color: #a78bfa !important; }
        .dark .text-yellow-600 { color: #fcd34d !important; }

        /* Form elements in dark mode */
        .dark input[type="date"] {
            background-color: #374151 !important;
            border-color: #6b7280 !important;
            color: #f3f4f6 !important;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-800 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-gray-800">Client Analytics: {{ summary.client_name }}</h1>
                <div class="flex space-x-4 items-center">
                    <a href="{{ url_for('analytics_dashboard') }}" class="text-blue-600 dark:text-blue-400 hover:underline">&larr; Back to Analytics</a>
                    <a href="{{ url_for('admin_dashboard') }}" class="text-blue-600 dark:text-blue-400 hover:underline">Admin Dashboard</a>
                    <button id="theme-toggle" class="ml-2 p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <span id="theme-icon" class="text-xl">🌙</span>
                    </button>
                </div>
            </div>

            <!-- Date Range Filter -->
            <div class="mb-6">
                <form id="dateRangeForm" class="flex flex-wrap items-end gap-4" method="get">
                    <input type="hidden" name="client" value="{{ summary.client_name }}">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input type="date" id="start_date" name="start_date" value="{{ start_date }}"
                               class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                        <input type="date" id="end_date" name="end_date" value="{{ end_date }}"
                               class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            Apply Filter
                        </button>
                    </div>
                    <div>
                        <a href="{{ url_for('client_analytics', client=summary.client_name) }}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            Reset
                        </a>
                    </div>
                </form>
            </div>

            <!-- Summary Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-blue-50 p-6 rounded-lg border border-blue-100">
                    <h3 class="text-lg font-semibold text-blue-800 mb-2">Total Queries</h3>
                    <p class="text-3xl font-bold text-blue-600">{{ summary.total_queries|default(0) }}</p>
                </div>
                <div class="bg-green-50 p-6 rounded-lg border border-green-100">
                    <h3 class="text-lg font-semibold text-green-800 mb-2">Total Sessions</h3>
                    <p class="text-3xl font-bold text-green-600">{{ summary.total_sessions|default(0) }}</p>
                </div>
                <div class="bg-purple-50 p-6 rounded-lg border border-purple-100">
                    <h3 class="text-lg font-semibold text-purple-800 mb-2">Avg. Question Length</h3>
                    <p class="text-3xl font-bold text-purple-600">{{ summary.avg_question_length|default(0)|round|int }} chars</p>
                </div>
                <div class="bg-yellow-50 p-6 rounded-lg border border-yellow-100">
                    <h3 class="text-lg font-semibold text-yellow-800 mb-2">Avg. Processing Time</h3>
                    <p class="text-3xl font-bold text-yellow-600">
                        {% if summary.avg_processing_time is not none %}
                            {{ "%.2f"|format(summary.avg_processing_time) }}s
                        {% else %}
                            0.00s
                        {% endif %}
                    </p>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Activity Over Time Chart -->
                <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Activity Over Time</h3>
                    <div class="h-64">
                        <canvas id="activityChart"></canvas>
                    </div>
                </div>

                <!-- Category Distribution Chart -->
                <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Category Distribution</h3>
                    <div class="h-64">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Common Questions -->
            <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Common Questions</h3>
                {% if summary.common_questions %}
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Question</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for question in summary.common_questions %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 text-sm text-gray-900">{{ question.question }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ question.count }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-gray-500 italic">No common questions data available</p>
                {% endif %}
            </div>

            <!-- Recent Analytics Table -->
            <div class="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Queries</h3>
                {% if analytics %}
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Processing Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sources</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for entry in analytics[:15] %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ entry.timestamp }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ entry.category }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {% if entry.processing_time is not none %}
                                                {{ "%.2f"|format(entry.processing_time) }}s
                                            {% else %}
                                                N/A
                                            {% endif %}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ entry.source_count }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ entry.model_name }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-yellow-700">
                                    No analytics data available for this client.
                                </p>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script>
        // Chart data from server
        const activityData = {{ activity_data|tojson }};
        const categoryData = {{ category_data|tojson }};

        // Initialize charts when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize theme using utilities.js
            DMSUtils.initDarkMode();

            // Add theme toggle button event listener
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', function() {
                    const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                                      document.documentElement.classList.contains('dark');
                    DMSUtils.toggleDarkMode(!isDarkMode);

                    // Update charts with new theme
                    updateChartsForTheme(!isDarkMode);
                });
            }

            // Set initial theme icon based on current theme
            const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                              document.documentElement.classList.contains('dark');
            const themeIcon = document.getElementById('theme-icon');
            if (themeIcon) {
                themeIcon.textContent = isDarkMode ? '☀️' : '🌙';
            }
            // Activity Chart
            const activityCtx = document.getElementById('activityChart').getContext('2d');
            new Chart(activityCtx, {
                type: 'line',
                data: {
                    labels: activityData.labels,
                    datasets: [{
                        label: 'Number of Queries',
                        data: activityData.values,
                        borderColor: 'rgb(79, 70, 229)',
                        backgroundColor: 'rgba(79, 70, 229, 0.1)',
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Count'
                            }
                        }
                    }
                }
            });

            // Category Chart
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            new Chart(categoryCtx, {
                type: 'doughnut',
                data: {
                    labels: categoryData.labels,
                    datasets: [{
                        data: categoryData.values,
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(199, 199, 199, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        }
                    }
                }
            });

            // Initialize charts with current theme
            updateChartsForTheme(isDarkMode);
        });

        // Function to update chart colors based on theme
        function updateChartsForTheme(isDark) {
            // Get all chart instances
            const charts = Object.values(Chart.instances);

            // Update each chart with theme-appropriate colors
            charts.forEach(chart => {
                if (chart.config.type === 'line') {
                    // Update line chart colors
                    chart.data.datasets.forEach(dataset => {
                        if (isDark) {
                            dataset.borderColor = 'rgb(96, 165, 250)';  // Lighter blue in dark mode
                            dataset.backgroundColor = 'rgba(96, 165, 250, 0.2)';
                        } else {
                            dataset.borderColor = 'rgb(79, 70, 229)';  // Original color
                            dataset.backgroundColor = 'rgba(79, 70, 229, 0.1)';
                        }
                    });
                } else if (chart.config.type === 'doughnut') {
                    // Update doughnut chart colors
                    if (isDark) {
                        chart.data.datasets[0].backgroundColor = [
                            'rgba(96, 165, 250, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(252, 211, 77, 0.8)',
                            'rgba(52, 211, 153, 0.8)',
                            'rgba(167, 139, 250, 0.8)',
                            'rgba(251, 146, 60, 0.8)',
                            'rgba(156, 163, 175, 0.8)'
                        ];
                    } else {
                        chart.data.datasets[0].backgroundColor = [
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(199, 199, 199, 0.7)'
                        ];
                    }
                }

                // Update chart options for better visibility in dark mode
                if (chart.options.scales && chart.options.scales.y) {
                    chart.options.scales.y.grid = {
                        color: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    };

                    chart.options.scales.y.ticks = {
                        color: isDark ? '#e5e7eb' : '#4b5563'
                    };
                }

                if (chart.options.scales && chart.options.scales.x) {
                    chart.options.scales.x.grid = {
                        color: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    };

                    chart.options.scales.x.ticks = {
                        color: isDark ? '#e5e7eb' : '#4b5563'
                    };
                }

                // Update legend colors
                if (chart.options.plugins && chart.options.plugins.legend) {
                    chart.options.plugins.legend.labels = {
                        color: isDark ? '#e5e7eb' : '#4b5563'
                    };
                }

                // Update the chart
                chart.update();
            });
        }
    </script>
</body>
</html>
